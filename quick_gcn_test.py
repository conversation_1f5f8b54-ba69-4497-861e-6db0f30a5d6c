#!/usr/bin/env python3
"""
快速测试GCN替换CNN的功能
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from models.decoder import PositionwiseFeedforward, DecoderLayer
from models.gnn import GCNLayer

def test_basic_functionality():
    """测试基本功能"""
    print("测试GCN替换CNN的基本功能...")
    
    # 测试参数
    hid_dim = 64
    pf_dim = 256
    dropout = 0.1
    batch_size = 2
    sent_len = 10
    
    # 创建PositionwiseFeedforward (GCN版本)
    pf = PositionwiseFeedforward(hid_dim, pf_dim, dropout)
    pf.eval()
    
    # 创建测试输入
    x = torch.randn(batch_size, sent_len, hid_dim)
    
    print(f"输入维度: {x.shape}")
    
    try:
        # 前向传播
        with torch.no_grad():
            output = pf(x)
        
        print(f"输出维度: {output.shape}")
        
        # 验证输出
        if output.shape == x.shape:
            print("✓ 输出维度正确")
        else:
            print(f"✗ 输出维度错误: 期望 {x.shape}, 实际 {output.shape}")
            return False
            
        if torch.isnan(output).any() or torch.isinf(output).any():
            print("✗ 输出包含无效数值")
            return False
        else:
            print("✓ 输出数值有效")
            
        print("✓ PositionwiseFeedforward (GCN版本) 测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_decoder_layer():
    """测试DecoderLayer"""
    print("\n测试DecoderLayer...")
    
    # 测试参数
    hid_dim = 64
    n_heads = 4
    dropout = 0.1
    batch_size = 2
    seq_len = 10
    
    # 创建DecoderLayer
    decoder = DecoderLayer(hid_dim, n_heads, dropout)
    decoder.eval()
    
    # 创建测试输入
    trg = torch.randn(batch_size, seq_len, hid_dim)
    src = torch.randn(batch_size, seq_len, hid_dim)
    
    # 创建简单的mask
    trg_mask = torch.ones(batch_size, 1, seq_len, seq_len)
    src_mask = torch.ones(batch_size, 1, seq_len, seq_len)
    
    print(f"目标输入维度: {trg.shape}")
    print(f"源输入维度: {src.shape}")
    
    try:
        with torch.no_grad():
            output = decoder(trg, src, trg_mask, src_mask)
        
        print(f"输出维度: {output.shape}")
        
        if output.shape == trg.shape:
            print("✓ 输出维度正确")
        else:
            print(f"✗ 输出维度错误: 期望 {trg.shape}, 实际 {output.shape}")
            return False
            
        if torch.isnan(output).any() or torch.isinf(output).any():
            print("✗ 输出包含无效数值")
            return False
        else:
            print("✓ 输出数值有效")
            
        print("✓ DecoderLayer 测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def compare_performance():
    """比较GCN和Conv1d的性能"""
    print("\n比较GCN和Conv1d的输出...")
    
    # 创建原始Conv1d版本
    class OriginalPositionwiseFeedforward(nn.Module):
        def __init__(self, hid_dim, pf_dim, dropout):
            super().__init__()
            self.fc_1 = nn.Conv1d(hid_dim, pf_dim, 1)
            self.fc_2 = nn.Conv1d(pf_dim, hid_dim, 1)
            self.do = nn.Dropout(dropout)

        def forward(self, x):
            x = x.permute(0, 2, 1)
            x = self.do(F.relu(self.fc_1(x)))
            x = self.fc_2(x)
            x = x.permute(0, 2, 1)
            return x
    
    # 测试参数
    hid_dim = 32
    pf_dim = 128
    dropout = 0.0  # 关闭dropout
    batch_size = 2
    sent_len = 8
    
    # 创建两个版本
    conv_pf = OriginalPositionwiseFeedforward(hid_dim, pf_dim, dropout)
    gcn_pf = PositionwiseFeedforward(hid_dim, pf_dim, dropout)
    
    conv_pf.eval()
    gcn_pf.eval()
    
    # 创建测试输入
    torch.manual_seed(42)
    x = torch.randn(batch_size, sent_len, hid_dim)
    
    try:
        with torch.no_grad():
            conv_output = conv_pf(x)
            gcn_output = gcn_pf(x)
        
        print(f"Conv1d输出维度: {conv_output.shape}")
        print(f"GCN输出维度: {gcn_output.shape}")
        
        # 计算统计信息
        print(f"Conv1d输出均值: {conv_output.mean().item():.6f}")
        print(f"GCN输出均值: {gcn_output.mean().item():.6f}")
        print(f"Conv1d输出标准差: {conv_output.std().item():.6f}")
        print(f"GCN输出标准差: {gcn_output.std().item():.6f}")
        
        print("✓ 性能比较完成")
        return True
        
    except Exception as e:
        print(f"✗ 比较测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("GCN替换CNN功能测试")
    print("=" * 50)
    
    tests = [
        ("基本功能", test_basic_functionality),
        ("DecoderLayer", test_decoder_layer),
        ("性能比较", compare_performance)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n[{test_name}]")
        if test_func():
            passed += 1
        else:
            print(f"✗ {test_name} 测试失败")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！GCN替换CNN成功。")
    else:
        print("⚠️ 部分测试失败，请检查代码。")
    
    return passed == total

if __name__ == "__main__":
    main()
