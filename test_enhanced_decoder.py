import torch
import sys
import os
import numpy as np

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_drug_decoder():
    """测试增强的药物解码器"""
    print("=== 测试增强的药物解码器 ===")
    
    try:
        from models.enhanced_drug_decoder import EnhancedDrugDecoder
        print("✓ 成功导入EnhancedDrugDecoder")
        
        # 设置测试参数
        batch_size = 4
        num_nodes = 20  # 图节点数
        seq_len = 50    # 序列长度
        struct_input_dim = 78   # 图结构特征维度
        seq_input_dim = 384     # 序列特征维度
        hidden_dim = 128
        output_dim = 128
        
        # 创建模型
        decoder = EnhancedDrugDecoder(
            struct_input_dim=struct_input_dim,
            seq_input_dim=seq_input_dim,
            hidden_dim=hidden_dim,
            output_dim=output_dim,
            num_heads=8,
            dropout=0.1
        )
        print(f"✓ 成功创建EnhancedDrugDecoder模型")
        
        # 创建测试数据
        struct_features = torch.randn(batch_size, num_nodes, struct_input_dim)
        seq_features = torch.randn(batch_size, seq_len, seq_input_dim)
        
        # 创建邻接矩阵（对称且归一化）
        adj_matrix = torch.randn(batch_size, num_nodes, num_nodes)
        adj_matrix = torch.softmax(adj_matrix, dim=-1)  # 行归一化
        
        # 创建掩码
        seq_mask = torch.zeros(batch_size, seq_len, dtype=torch.bool)
        struct_mask = torch.zeros(batch_size, num_nodes, dtype=torch.bool)
        
        # 为一些样本添加掩码（模拟变长序列）
        seq_mask[0, 30:] = True  # 第一个样本只有30个有效token
        seq_mask[1, 40:] = True  # 第二个样本只有40个有效token
        struct_mask[0, 15:] = True  # 第一个样本只有15个有效节点
        struct_mask[1, 18:] = True  # 第二个样本只有18个有效节点
        
        print(f"✓ 创建测试数据:")
        print(f"  - 结构特征: {struct_features.shape}")
        print(f"  - 序列特征: {seq_features.shape}")
        print(f"  - 邻接矩阵: {adj_matrix.shape}")
        print(f"  - 序列掩码: {seq_mask.shape}")
        print(f"  - 结构掩码: {struct_mask.shape}")
        
        # 前向传播
        with torch.no_grad():
            output, debug_info = decoder(
                struct_features=struct_features,
                seq_features=seq_features,
                adj_matrix=adj_matrix,
                seq_mask=seq_mask,
                struct_mask=struct_mask
            )
        
        print(f"✓ 前向传播成功")
        print(f"  - 输出形状: {output.shape}")
        print(f"  - 期望形状: ({batch_size}, {seq_len}, {output_dim})")
        
        # 检查调试信息
        print(f"✓ 调试信息:")
        for key, value in debug_info.items():
            if isinstance(value, torch.Tensor):
                print(f"  - {key}: {value.shape}")
            elif isinstance(value, list):
                print(f"  - {key}: list of {len(value)} tensors")
                for i, tensor in enumerate(value):
                    print(f"    - layer {i}: {tensor.shape}")
        
        # 验证输出形状
        expected_shape = (batch_size, seq_len, output_dim)
        assert output.shape == expected_shape, f"输出形状不匹配: {output.shape} vs {expected_shape}"
        print(f"✓ 输出形状验证通过")
        
        # 验证输出值的合理性
        assert not torch.isnan(output).any(), "输出包含NaN值"
        assert not torch.isinf(output).any(), "输出包含无穷值"
        print(f"✓ 输出值验证通过")
        
        # 测试梯度计算
        print("\n=== 测试梯度计算 ===")
        decoder.train()
        
        # 创建一个简单的损失
        target = torch.randn_like(output)
        loss = F.mse_loss(output, target)
        
        # 反向传播
        loss.backward()
        print(f"✓ 梯度计算成功，损失值: {loss.item():.6f}")
        
        # 检查梯度
        has_grad = False
        for name, param in decoder.named_parameters():
            if param.grad is not None:
                has_grad = True
                grad_norm = param.grad.norm().item()
                print(f"  - {name}: 梯度范数 = {grad_norm:.6f}")
                break
        
        assert has_grad, "没有参数有梯度"
        print(f"✓ 梯度检查通过")
        
        print("\n=== 所有测试通过! ===")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_integration_with_main_model():
    """测试与主模型的集成"""
    print("\n=== 测试与主模型的集成 ===")
    
    try:
        import argparse
        from models.core import PMMRNet
        
        # 创建测试参数
        args = argparse.Namespace()
        args.compound_gnn_dim = 78
        args.dropout = 0.2
        args.decoder_dim = 128
        args.decoder_heads = 4
        args.compound_text_dim = 128
        args.compound_structure_dim = 78
        args.protein_dim = 128
        args.linear_heads = 10
        args.linear_hidden_dim = 32
        args.pf_dim = 1024
        args.encoder_heads = 4
        args.encoder_layers = 1
        args.protein_pretrained_dim = 480
        args.compound_pretrained_dim = 384
        args.objective = 'regression'
        
        # 创建模型
        model = PMMRNet(args)
        print("✓ 成功创建集成的PMMRNet模型")
        
        # 检查增强解码器是否正确集成
        assert hasattr(model, 'enhanced_drug_decoder'), "模型中没有enhanced_drug_decoder属性"
        print("✓ 增强药物解码器已正确集成")
        
        # 创建测试数据
        batch_size = 2
        data = {
            'COMPOUND_NODE_FEAT': torch.randn(batch_size, 20, 78),
            'COMPOUND_ADJ': torch.softmax(torch.randn(batch_size, 20, 20), dim=-1),
            'COMPOUND_EMBEDDING': torch.randn(batch_size, 50, 384),
            'PROTEIN_EMBEDDING': torch.randn(batch_size, 100, 480),
            'COMPOUND_NODE_NUM': [15, 18],
            'COMPOUND_SMILES_LENGTH': [30, 40],
            'PROTEIN_NODE_NUM': [80, 90],
            'LABEL': torch.randn(batch_size, 1)
        }
        
        print("✓ 创建测试数据")
        
        # 前向传播测试
        model.eval()
        with torch.no_grad():
            output = model(data)
        
        print(f"✓ 集成模型前向传播成功")
        print(f"  - 输出形状: {output.shape}")
        
        # 验证输出
        if args.objective == 'regression':
            expected_shape = (batch_size, 1)
        else:
            expected_shape = (batch_size, 2)
            
        assert output.shape == expected_shape, f"输出形状不匹配: {output.shape} vs {expected_shape}"
        print(f"✓ 集成模型输出形状验证通过")
        
        print("✓ 与主模型的集成测试通过!")
        return True
        
    except Exception as e:
        print(f"✗ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    print("开始测试增强的药物解码器...")
    
    # 导入必要的模块
    import torch.nn.functional as F
    
    # 运行测试
    test1_passed = test_enhanced_drug_decoder()
    test2_passed = test_integration_with_main_model()
    
    if test1_passed and test2_passed:
        print("\n🎉 所有测试都通过了!")
    else:
        print("\n❌ 部分测试失败，请检查错误信息")
