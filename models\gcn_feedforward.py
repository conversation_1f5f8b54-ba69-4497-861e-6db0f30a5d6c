import torch
import torch.nn as nn
import torch.nn.functional as F
import math

class GraphConvolution(nn.Module):
    """高效的图卷积层实现"""
    def __init__(self, in_features, out_features, bias=True):
        super(GraphConvolution, self).__init__()
        self.in_features = in_features
        self.out_features = out_features
        self.weight = nn.Parameter(torch.FloatTensor(in_features, out_features))
        if bias:
            self.bias = nn.Parameter(torch.FloatTensor(out_features))
        else:
            self.register_parameter('bias', None)
        self.reset_parameters()

    def reset_parameters(self):
        stdv = 1. / math.sqrt(self.weight.size(1))
        self.weight.data.uniform_(-stdv, stdv)
        if self.bias is not None:
            self.bias.data.uniform_(-stdv, stdv)

    def forward(self, input, adj):
        # input: [batch_size, seq_len, in_features] or [seq_len, in_features]
        # adj: [seq_len, seq_len]
        
        if input.dim() == 3:
            # 批量处理
            batch_size, seq_len, in_features = input.shape
            # 重塑为 [batch_size * seq_len, in_features]
            input_flat = input.view(-1, in_features)
            # 应用线性变换
            support = torch.mm(input_flat, self.weight)
            # 重塑回 [batch_size, seq_len, out_features]
            support = support.view(batch_size, seq_len, -1)
            # 批量应用邻接矩阵
            output = torch.bmm(adj.unsqueeze(0).expand(batch_size, -1, -1), support)
        else:
            # 单个图处理
            support = torch.mm(input, self.weight)
            output = torch.mm(adj, support)
        
        if self.bias is not None:
            return output + self.bias
        else:
            return output

class EfficientGCNFeedforward(nn.Module):
    """高效的GCN前馈网络，替换Conv1d"""
    def __init__(self, hid_dim, pf_dim, dropout, adjacency_type='local'):
        super().__init__()
        
        self.hid_dim = hid_dim
        self.pf_dim = pf_dim
        self.adjacency_type = adjacency_type
        
        # GCN层
        self.gcn1 = GraphConvolution(hid_dim, pf_dim)
        self.gcn2 = GraphConvolution(pf_dim, hid_dim)
        
        self.dropout = nn.Dropout(dropout)
        
        # 预计算的邻接矩阵缓存
        self.register_buffer('cached_adj', None)
        self.cached_seq_len = None
    
    def create_adjacency_matrix(self, seq_len, device, adjacency_type='local'):
        """创建邻接矩阵"""
        if adjacency_type == 'local':
            # 局部连接（类似Conv1d的感受野）
            adj = torch.zeros(seq_len, seq_len, device=device)
            for i in range(seq_len):
                # 连接到邻近的3个位置（包括自己）
                start = max(0, i - 1)
                end = min(seq_len, i + 2)
                adj[i, start:end] = 1.0
        elif adjacency_type == 'full':
            # 全连接
            adj = torch.ones(seq_len, seq_len, device=device)
        elif adjacency_type == 'identity':
            # 只有自连接（退化为普通的线性层）
            adj = torch.eye(seq_len, device=device)
        else:
            raise ValueError(f"Unknown adjacency type: {adjacency_type}")
        
        # 行归一化
        row_sum = adj.sum(dim=1, keepdim=True)
        adj = adj / (row_sum + 1e-8)
        
        return adj
    
    def get_adjacency_matrix(self, seq_len, device):
        """获取邻接矩阵（带缓存）"""
        if self.cached_adj is None or self.cached_seq_len != seq_len:
            adj = self.create_adjacency_matrix(seq_len, device, self.adjacency_type)
            self.register_buffer('cached_adj', adj)
            self.cached_seq_len = seq_len
            return adj
        else:
            return self.cached_adj
    
    def forward(self, x):
        # x: [batch_size, seq_len, hid_dim]
        batch_size, seq_len, hid_dim = x.shape
        
        # 获取邻接矩阵
        adj = self.get_adjacency_matrix(seq_len, x.device)
        
        # 第一层GCN
        h1 = self.gcn1(x, adj)  # [batch_size, seq_len, pf_dim]
        h1 = F.relu(h1)
        h1 = self.dropout(h1)
        
        # 第二层GCN
        h2 = self.gcn2(h1, adj)  # [batch_size, seq_len, hid_dim]
        
        return h2

class AdaptiveGCNFeedforward(nn.Module):
    """自适应GCN前馈网络，可以学习邻接矩阵"""
    def __init__(self, hid_dim, pf_dim, dropout):
        super().__init__()
        
        self.hid_dim = hid_dim
        self.pf_dim = pf_dim
        
        # 标准的线性变换
        self.linear1 = nn.Linear(hid_dim, pf_dim)
        self.linear2 = nn.Linear(pf_dim, hid_dim)
        
        # 学习邻接矩阵的参数
        self.adj_weight = nn.Parameter(torch.randn(1, 1, 1))
        
        self.dropout = nn.Dropout(dropout)
    
    def create_learnable_adjacency(self, seq_len, device):
        """创建可学习的邻接矩阵"""
        # 创建基础的局部连接模式
        base_adj = torch.zeros(seq_len, seq_len, device=device)
        for i in range(seq_len):
            start = max(0, i - 1)
            end = min(seq_len, i + 2)
            base_adj[i, start:end] = 1.0
        
        # 应用可学习的权重
        adj = base_adj * torch.sigmoid(self.adj_weight)
        
        # 添加自连接
        adj = adj + torch.eye(seq_len, device=device) * (1 - torch.sigmoid(self.adj_weight))
        
        # 归一化
        row_sum = adj.sum(dim=1, keepdim=True)
        adj = adj / (row_sum + 1e-8)
        
        return adj
    
    def forward(self, x):
        # x: [batch_size, seq_len, hid_dim]
        batch_size, seq_len, hid_dim = x.shape
        
        # 创建可学习的邻接矩阵
        adj = self.create_learnable_adjacency(seq_len, x.device)
        
        # 第一层：线性变换 + 图卷积
        h1 = self.linear1(x)  # [batch_size, seq_len, pf_dim]
        h1 = torch.bmm(adj.unsqueeze(0).expand(batch_size, -1, -1), h1)
        h1 = F.relu(h1)
        h1 = self.dropout(h1)
        
        # 第二层：线性变换 + 图卷积
        h2 = self.linear2(h1)  # [batch_size, seq_len, hid_dim]
        h2 = torch.bmm(adj.unsqueeze(0).expand(batch_size, -1, -1), h2)
        
        return h2

# 为了方便使用，创建一个统一的接口
class GCNPositionwiseFeedforward(nn.Module):
    """统一的GCN前馈网络接口"""
    def __init__(self, hid_dim, pf_dim, dropout, gcn_type='efficient_local'):
        super().__init__()
        
        if gcn_type == 'efficient_local':
            self.impl = EfficientGCNFeedforward(hid_dim, pf_dim, dropout, 'local')
        elif gcn_type == 'efficient_full':
            self.impl = EfficientGCNFeedforward(hid_dim, pf_dim, dropout, 'full')
        elif gcn_type == 'adaptive':
            self.impl = AdaptiveGCNFeedforward(hid_dim, pf_dim, dropout)
        else:
            raise ValueError(f"Unknown GCN type: {gcn_type}")
    
    def forward(self, x):
        return self.impl(x)
