import math
import torch
from torch import nn
import torch.nn.functional as F
from models.gnn import MultiGIN, GINLayer
from models.decoder import SelfAttention
try:
    from models.gcn_feedforward import GCNPositionwiseFeedforward
except ImportError:
    GCNPositionwiseFeedforward = None


class MultiScaleGraphEncoder(nn.Module):
    """多尺度图结构特征编码器"""
    def __init__(self, input_dim, hidden_dim, output_dim, num_layers=3):
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        self.num_layers = num_layers
        
        # 多个不同尺度的GIN层
        self.gin_layers = nn.ModuleList()
        self.gin_layers.append(GINLayer(input_dim, hidden_dim))
        for _ in range(num_layers - 2):
            self.gin_layers.append(GINLayer(hidden_dim, hidden_dim))
        self.gin_layers.append(GINLayer(hidden_dim, output_dim))
        
        # 跳跃连接的投影层
        self.skip_projections = nn.ModuleList()
        self.skip_projections.append(nn.Linear(input_dim, output_dim))
        for _ in range(num_layers - 2):
            self.skip_projections.append(nn.Linear(hidden_dim, output_dim))
        
        # 特征融合层
        self.feature_fusion = nn.Sequential(
            nn.Linear(output_dim * num_layers, output_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(output_dim, output_dim)
        )
        
        # 层归一化
        self.layer_norms = nn.ModuleList([nn.LayerNorm(output_dim) for _ in range(num_layers)])
        
    def forward(self, x, adj):
        """
        x: [batch_size, num_nodes, input_dim]
        adj: [batch_size, num_nodes, num_nodes]
        """
        layer_outputs = []
        current_x = x
        
        for i, (gin_layer, skip_proj, ln) in enumerate(zip(self.gin_layers, self.skip_projections, self.layer_norms)):
            # GIN层处理
            gin_out = gin_layer(adj, current_x)
            gin_out = F.relu(gin_out)
            
            # 跳跃连接
            if i == 0:
                skip_out = skip_proj(x)
            else:
                skip_out = skip_proj(current_x)
            
            # 残差连接和层归一化
            layer_out = ln(gin_out + skip_out)
            layer_outputs.append(layer_out)
            current_x = layer_out
        
        # 多尺度特征融合
        concatenated = torch.cat(layer_outputs, dim=-1)
        fused_features = self.feature_fusion(concatenated)
        
        return fused_features, layer_outputs


class SequenceStructureAlignment(nn.Module):
    """序列-结构特征对齐模块"""
    def __init__(self, seq_dim, struct_dim, hidden_dim, num_heads=8):
        super().__init__()
        self.seq_dim = seq_dim
        self.struct_dim = struct_dim
        self.hidden_dim = hidden_dim
        self.num_heads = num_heads
        
        # 特征投影到统一维度
        self.seq_proj = nn.Linear(seq_dim, hidden_dim)
        self.struct_proj = nn.Linear(struct_dim, hidden_dim)
        
        # 双向交叉注意力
        self.seq_to_struct_attn = SelfAttention(hidden_dim, num_heads, 0.1)
        self.struct_to_seq_attn = SelfAttention(hidden_dim, num_heads, 0.1)
        
        # 特征增强层
        self.seq_enhance = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim)
        )
        
        self.struct_enhance = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim)
        )
        
        # 层归一化
        self.seq_ln = nn.LayerNorm(hidden_dim)
        self.struct_ln = nn.LayerNorm(hidden_dim)
        
    def forward(self, seq_features, struct_features, seq_mask=None, struct_mask=None):
        """
        seq_features: [batch_size, seq_len, seq_dim]
        struct_features: [batch_size, num_nodes, struct_dim]
        """
        # 投影到统一维度
        seq_proj = self.seq_proj(seq_features)  # [batch_size, seq_len, hidden_dim]
        struct_proj = self.struct_proj(struct_features)  # [batch_size, num_nodes, hidden_dim]
        
        # 双向交叉注意力
        seq_attended = self.seq_to_struct_attn(seq_proj, struct_proj, struct_proj, struct_mask)
        struct_attended = self.struct_to_seq_attn(struct_proj, seq_proj, seq_proj, seq_mask)
        
        # 特征融合和增强
        seq_enhanced = self.seq_enhance(torch.cat([seq_proj, seq_attended], dim=-1))
        struct_enhanced = self.struct_enhance(torch.cat([struct_proj, struct_attended], dim=-1))
        
        # 残差连接和层归一化
        seq_output = self.seq_ln(seq_proj + seq_enhanced)
        struct_output = self.struct_ln(struct_proj + struct_enhanced)
        
        return seq_output, struct_output


class AdaptiveFusionModule(nn.Module):
    """自适应特征融合模块"""
    def __init__(self, feature_dim, num_features=2):
        super().__init__()
        self.feature_dim = feature_dim
        self.num_features = num_features
        
        # 特征重要性学习
        self.importance_net = nn.Sequential(
            nn.Linear(feature_dim * num_features, feature_dim),
            nn.ReLU(),
            nn.Linear(feature_dim, num_features),
            nn.Softmax(dim=-1)
        )
        
        # 特征交互学习
        self.interaction_net = nn.Sequential(
            nn.Linear(feature_dim * num_features, feature_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(feature_dim * 2, feature_dim)
        )
        
        # 门控机制
        self.gate = nn.Sequential(
            nn.Linear(feature_dim * num_features, feature_dim),
            nn.Sigmoid()
        )
        
    def forward(self, features_list):
        """
        features_list: list of [batch_size, seq_len, feature_dim]
        """
        # 连接所有特征
        concatenated = torch.cat(features_list, dim=-1)  # [batch_size, seq_len, feature_dim * num_features]
        
        # 学习特征重要性权重
        importance_weights = self.importance_net(concatenated)  # [batch_size, seq_len, num_features]
        
        # 加权融合
        weighted_features = []
        for i, feature in enumerate(features_list):
            weight = importance_weights[:, :, i:i+1]  # [batch_size, seq_len, 1]
            weighted_features.append(feature * weight)
        
        weighted_sum = sum(weighted_features)  # [batch_size, seq_len, feature_dim]
        
        # 特征交互
        interaction_features = self.interaction_net(concatenated)
        
        # 门控融合
        gate_weights = self.gate(concatenated)
        fused_features = gate_weights * weighted_sum + (1 - gate_weights) * interaction_features
        
        return fused_features


class EnhancedDrugDecoder(nn.Module):
    """增强的药物解码器模块"""
    def __init__(self, 
                 struct_input_dim=78,
                 seq_input_dim=384, 
                 hidden_dim=128,
                 output_dim=128,
                 num_heads=8,
                 dropout=0.1):
        super().__init__()
        
        self.struct_input_dim = struct_input_dim
        self.seq_input_dim = seq_input_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        
        # 多尺度图结构编码器
        self.graph_encoder = MultiScaleGraphEncoder(
            input_dim=struct_input_dim,
            hidden_dim=hidden_dim,
            output_dim=hidden_dim,
            num_layers=3
        )
        
        # 序列特征编码器（Transformer）
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=hidden_dim,
            nhead=num_heads,
            dim_feedforward=hidden_dim * 4,
            dropout=dropout,
            batch_first=True
        )
        self.seq_encoder = nn.TransformerEncoder(encoder_layer, num_layers=2)
        
        # 序列特征投影
        self.seq_proj = nn.Linear(seq_input_dim, hidden_dim)
        
        # 序列-结构对齐模块
        self.alignment_module = SequenceStructureAlignment(
            seq_dim=hidden_dim,
            struct_dim=hidden_dim,
            hidden_dim=hidden_dim,
            num_heads=num_heads
        )
        
        # 自适应融合模块
        self.fusion_module = AdaptiveFusionModule(
            feature_dim=hidden_dim,
            num_features=2
        )
        
        # 最终输出投影
        self.output_proj = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, output_dim)
        )
        
        # 层归一化
        self.final_ln = nn.LayerNorm(output_dim)
        
    def forward(self, struct_features, seq_features, adj_matrix, seq_mask=None, struct_mask=None):
        """
        struct_features: [batch_size, num_nodes, struct_input_dim] - 图结构特征
        seq_features: [batch_size, seq_len, seq_input_dim] - SMILES序列特征
        adj_matrix: [batch_size, num_nodes, num_nodes] - 邻接矩阵
        seq_mask: [batch_size, seq_len] - 序列掩码
        struct_mask: [batch_size, num_nodes] - 结构掩码
        """
        
        # 1. 多尺度图结构特征编码
        struct_encoded, struct_layers = self.graph_encoder(struct_features, adj_matrix)
        
        # 2. 序列特征编码
        seq_projected = self.seq_proj(seq_features)
        seq_encoded = self.seq_encoder(seq_projected, src_key_padding_mask=seq_mask)
        
        # 3. 序列-结构特征对齐
        seq_aligned, struct_aligned = self.alignment_module(
            seq_encoded, struct_encoded, seq_mask, struct_mask
        )
        
        # 4. 自适应特征融合
        # 需要确保两个特征的序列长度一致，这里使用平均池化对齐
        if seq_aligned.size(1) != struct_aligned.size(1):
            # 将结构特征池化到序列长度
            struct_pooled = F.adaptive_avg_pool1d(
                struct_aligned.transpose(1, 2), 
                seq_aligned.size(1)
            ).transpose(1, 2)
        else:
            struct_pooled = struct_aligned
            
        fused_features = self.fusion_module([seq_aligned, struct_pooled])
        
        # 5. 最终输出投影
        output = self.output_proj(fused_features)
        output = self.final_ln(output)
        
        return output, {
            'seq_encoded': seq_encoded,
            'struct_encoded': struct_encoded,
            'seq_aligned': seq_aligned,
            'struct_aligned': struct_aligned,
            'fused_features': fused_features,
            'struct_layers': struct_layers
        }
